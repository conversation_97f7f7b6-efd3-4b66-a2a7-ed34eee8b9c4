import type { Comment } from "@prisma/client";
import { db } from "@/db";
import {cache} from "react";

export type CommentWithAuthor = (Comment & {
  user: { name: string | null ; image: string | null };
 
});

export const  fetchCommentsByPostId = cache(async (postId: string): Promise<CommentWithAuthor[]>  => {
  return db.comment.findMany({
    where: { postId },
    include: {
      user: { select: { name: true, image: true } },
      // children: {
      //   include: {
      //     user: { select: { name: true } },
      //   },
      // },
    },
  });
} )
