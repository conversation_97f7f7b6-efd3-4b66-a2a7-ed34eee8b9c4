import type { Post } from '@prisma/client';
import { db } from '@/db';
import { Return } from '@prisma/client/runtime/library';

export type PostWithData = (Post & {
  user: {name: string | null};
  topic: {slug: string};
  _count: {comments: number};
});

//export type PostWithData = Awaited<ReturnType<typeof fetchPostsByTopicSlug>>[number];

export function searchPosts(term: string): Promise<PostWithData[]> {
  return db.post.findMany({
    where: {
      OR: [
        { title: { contains: term } },
        { content: { contains: term } },
        { user: { name: { contains: term } } },
      ],
    },
    include: {
      user: { select: { name: true, image: true } },
      topic: { select: { slug: true } },
      _count: { select: { comments: true } },
    },
  });
}

export function fetchPostsByTopicSlug(slug: string): Promise<PostWithData[]> {
 return db.post.findMany({
    where: { topic: { slug } },
    include: {
      user: { select: { name: true } },
      topic: { select: { slug: true } },
      _count: { select: { comments: true } },
    },
  });
}

export function fetchTopPosts(): Promise<PostWithData[]> {
  return db.post.findMany({
    orderBy: [{ comments: { _count: 'desc' } }],
    include: {
      user: { select: { name: true, image: true } },
      topic: { select: { slug: true } },
      _count: { select: { comments: true } },
    },
    take: 5,
  });
}
