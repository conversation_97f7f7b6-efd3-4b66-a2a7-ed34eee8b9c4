'use server';

import type { Post } from '@prisma/client';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';
import { db } from '@/db';
import paths from '@/paths';
import { z } from 'zod';
import { auth } from '@/auth';

const createPostSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters'),
  content: z.string().min(10, 'Content must be at least 10 characters'),
});

interface CreatePostFormState {
  errors: {
    title?: string[];
    content?: string[];
    _form?: string[];
  };
}

export async function createPost(
  slug: string,
  formState: CreatePostFormState,
  formData: FormData
): Promise<CreatePostFormState> {
  const title = formData.get('title') as string;
  const content = formData.get('content') as string;

  console.log('createPost', title, content, 'for topic:', slug);

  await new Promise((resolve) => setTimeout(resolve, 250));

  // Validate input
  const result = createPostSchema.safeParse({
    title,
    content,
  });

  if (!result.success) {
    console.log('createPost validation error', result.error);
    return {
      errors: result.error.flatten().fieldErrors,
    };
  }

  const session = await auth();
  console.log('createPost session', session);
  if (!session?.user?.id) {
    return {
      errors: {
        _form: ['You must be signed in to create a post'],
      },
    };
  }

  // Find the topic by slug
  const topic = await db.topic.findUnique({
    where: { slug },
  });

  if (!topic) {
    return {
      errors: {
        _form: ['Topic not found'],
      },
    };
  }

  let post: Post;
  try {
    post = await db.post.create({
      data: {
        title: result.data.title,
        content: result.data.content,
        userId: session.user.id,
        topicId: topic.id,
      },
    });

    console.log('Post created with title:', post.title);
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        errors: {
          _form: [error.message],
        },
      };
    } else {
      return {
        errors: {
          _form: ['Something went wrong'],
        },
      };
    }
  }

  // Revalidate and redirect after successful creation
  revalidatePath(paths.topicShow(slug));
  redirect(paths.postShow(slug, post.id));
}
