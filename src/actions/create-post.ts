'use server';

import type { Post } from '@prisma/client';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';
import { db } from '@/db';
import paths from '@/paths';
import {z} from 'zod';
import {auth} from '@/auth';

const createPostSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters'),
  content: z.string().min(10, 'Content must be at least 10 characters'),
});

interface CreatePostFormState {
  errors: {
    title?: string[];
    content?: string[];
    _form?: string[];
  };
}

export async function createPost(formState: CreatePostFormState, formData: FormData): Promise<CreatePostFormState> {
  const title = formData.get('title') as string;
  const content = formData.get('content') as string;

  console.log('createPost', title, content);

  await new Promise((resolve) => setTimeout(resolve, 250));

  // Validate input
  const result = createPostSchema.safeParse({
    title,
    content,
  });

  if (!result.success) {
    console.log('createPost validation error', result.error);
    return {
      errors: result.error.flatten().fieldErrors,
    };
  }

  const session = await auth();
  console.log('createPost session', session);
  if (!session?.user) {
    return {
      errors: {
        _form: ['You must be signed in to create a post'],
      },
    };
  }

  let post: Post;
  try {
    // TODO: Save to database
    post = await db.post.create({
      data: {
        title: result.data.title,
        content: result.data.content,
        userId: session.user.id,
        topicId: 'clj6111111111111111111111', // TODO: Get topic id from form
      },
    });

    console.log('Post would be created with title:', post.title);

    //TODO: Redirect to post page
    redirect(paths.postShow('javascript', post.id));
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        errors: {
          _form: [error.message],
        },
      };
    } else {
      return {
        errors: {
          _form: ['Something went wrong'],
        },
      };
    }
  }
  revalidatePath('/');
  redirect(paths.postShow('javascript', post.id)); 
}
