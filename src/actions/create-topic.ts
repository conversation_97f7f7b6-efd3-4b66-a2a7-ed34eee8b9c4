'use server';
import {z} from 'zod';

const createTopicSchema = z.object({
  name: z.string().min(3).regex(/^[a-z-]+$/, {message: 'Name must be lowercase or dashes with no spaces'}),
  description: z.string().min(10),
});

export async function createTopic(formState:number, formData: FormData) {
 
  const name = formData.get('name');
  const description = formData.get('description');
  console.log('createTopic', name, description);

  try {
    createTopicSchema.parse({name, description});
    return 10;
  } catch (e) {
    console.log('createTopic error', e);
    return {error: 'Invalid topic name or description'};
  }
}