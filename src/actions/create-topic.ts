'use server';
import { z } from 'zod';
import {auth} from '@/auth';
import type { Topic } from '@prisma/client';
import {redirect} from 'next/navigation';
import {revalidatePath} from 'next/cache';
import { db } from '@/db';
import paths from '@/paths';

const createTopicSchema = z.object({
  name: z
    .string()
    .min(3, 'Name must be at least 3 characters')
    .max(30, 'Name must be less than 30 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
});

interface CreateTopicFormState {
  errors: {
    name?: string[];
    description?: string[];
    _form?: string[];
  };
}

// Convert user input to URL-friendly slug
function createSlug(name: string): string {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with dashes
    .replace(/-+/g, '-') // Replace multiple dashes with single dash
    .replace(/^-|-$/g, ''); // Remove leading/trailing dashes
}

export async function createTopic(
  _formState: CreateTopicFormState,
  formData: FormData
): Promise<CreateTopicFormState> {
  const name = formData.get('name') as string;
  const description = formData.get('description') as string;

  console.log('createTopic', name, description);

  await new Promise((resolve) => setTimeout(resolve, 250));

  // Validate input
  const result = createTopicSchema.safeParse({
    name,
    description,
  });

  if (!result.success) {
    console.log('createTopic validation error', result.error);
    return {
      errors: result.error.flatten().fieldErrors,
    };
  }

  const session = await auth();
  console.log('createTopic session', session);
  if (!session?.user) {
    return {
      errors: {
        _form: ['You must be signed in to create a topic'],
      },
    };
  }

  const slug = createSlug(result.data.name);
  console.log('slug', slug, ' length ', slug.length);

  // Check if slug is valid after conversion
  if (!slug || slug.length < 3) {
    return {
      errors: {
        name: ['Name must contain at least 3 valid characters'],
      },
    };
  }
  let topic: Topic;
  try {
    // TODO: Save to database
    topic = await db.topic.create({
      data: {
        slug,
        description: result.data.description,
      },
    });

    console.log('Topic would be created with slug:', slug);

    //TODO: Redirect to topic page
    

  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        errors: {
          _form: [error.message],
        },
      };
    } else {
      return {
        errors: {
          _form: ['Something went wrong'],
        },
      };
    }
  }
  revalidatePath('/');
  redirect(paths.topicShow(topic.slug));
}
