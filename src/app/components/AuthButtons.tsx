'use client';

import { Button } from '@nextui-org/react';
import * as actions from '@/actions';

interface AuthButtonsProps {
  session: {
    user?: {
      name?: string | null;
      email?: string | null;
      image?: string | null;
    };
  } | null;
}

export default function AuthButtons({ session }: AuthButtonsProps) {
  return (
    <div className='flex gap-4'>
      {session?.user ? (
        <form action={actions.signOut}>
          <Button color='secondary' type='submit'>
            Sign Out
          </Button>
        </form>
      ) : (
        <form action={actions.signIn}>
          <Button color='primary' type='submit'>
            Sign In with GitHub
          </Button>
        </form>
      )}
    </div>
  );
}
