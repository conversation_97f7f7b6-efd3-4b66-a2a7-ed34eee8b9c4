'use client';

import { Input } from "@nextui-org/react";
import { useSearchParams } from "next/navigation";
import * as actions from '@/actions';

export default function SearchInput() {
  const searchParams = useSearchParams();
  const query = searchParams.get("term") || "";
  console.log('searchParams', searchParams);
  console.log('term', query);


  return (
    <form action={actions.search} className='w-full'>
      <Input label='Search' defaultValue={query} name='term' />
    </form>
  );
}
