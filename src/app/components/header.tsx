import Link from "next/link";
import { Navbar, NavbarBrand,NavbarContent, NavbarItem,Input,Button, Avatar } from "@nextui-org/react";
import { auth } from "@/auth";

export default async function Header() {
  const session = await auth();
  console.log('session', session);
  return (
    <Navbar className='shadow-mb-6'>
      <NavbarBrand>
        <Link href='/' className='font-bold'>
          <p>Discuss</p>
        </Link>
      </NavbarBrand>
      <NavbarContent justify="center">
        <NavbarItem>
          <Input />
        </NavbarItem>
      </NavbarContent>
      <NavbarContent justify="end">
        <NavbarItem>
          {
           session?.user ? <div>Signed In</div>: <div>Signed Out</div>
          }
        </NavbarItem>
      </NavbarContent>
    </Navbar>
  );
}
