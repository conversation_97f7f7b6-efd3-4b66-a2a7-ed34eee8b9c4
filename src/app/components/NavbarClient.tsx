'use client';

import Link from 'next/link';
import {
  Navbar,
  Navbar<PERSON>rand,
  NavbarContent,
  NavbarItem,
  Input,
  Button,
  Avatar,
  Popover,
  PopoverTrigger,
  PopoverContent,
} from '@nextui-org/react';
import React from 'react';
import * as actions from '@/actions';
import { useSession } from 'next-auth/react';

interface NavbarClientProps {
  session: {
    user?: {
      name?: string | null;
      email?: string | null;
      image?: string | null;
    };
  } | null;
}

// export default function NavbarClient({ session }: NavbarClientProps) {
  export default function NavbarClient() {
    const session = useSession();
    let authContent: React.ReactNode;
    console.log('session', session);
    if (session.data?.user) {
      authContent = (
        <Popover placement='right'>
          <PopoverTrigger>
            <Avatar
              src={session.data.user.image || ''}
              size='sm'
              className='w-8 h-8'
            />
          </PopoverTrigger>
          <PopoverContent>
            <div className='p-4'>
              <form action={actions.signOut}>
                <Button type='submit' color='secondary' variant='bordered'>
                  Sign Out
                </Button>
              </form>
            </div>
          </PopoverContent>
        </Popover>
      );
    } else {
      authContent = (
        <>
          <NavbarItem>
            <form action={actions.signIn}>
              <Button type='submit' color='secondary' variant='bordered'>
                Sign In
              </Button>
            </form>
          </NavbarItem>
          <NavbarItem>
            <form action={actions.signIn}>
              <Button type='submit' color='primary' variant='flat'>
                Sign Up
              </Button>
            </form>
          </NavbarItem>
        </>
      );
    }

    return (
      <Navbar className='shadow-mb-6'>
        <NavbarBrand>
          <Link href='/' className='font-bold'>
            <p>Discuss</p>
          </Link>
        </NavbarBrand>
        <NavbarContent justify='center'>
          <NavbarItem>
            <Input placeholder='Search topics...' />
          </NavbarItem>
        </NavbarContent>
        <NavbarContent justify='end'>{authContent}</NavbarContent>
      </Navbar>
    );
  }
