'use client';

import Link from "next/link";
import { Navbar, Nav<PERSON><PERSON>rand, NavbarContent, NavbarItem, Input, Button, Avatar , Popover, PopoverTrigger, PopoverContent} from "@nextui-org/react";
import { div } from "framer-motion/client";
import React from "react";
import * as actions from '@/actions';

interface NavbarClientProps {
  session: any;
}

export default function NavbarClient({ session }: NavbarClientProps) {
  let authContent:React.ReactNode;
  console.log('session', session);
  if (session?.user) {
    authContent = (
      <Popover placement='right'>
        <PopoverTrigger>
          <Avatar src={session.user.image || ''}size='sm'className='w-8 h-8'/>
        </PopoverTrigger>
        <PopoverContent>
          <div className='p-4'>
            <form action={actions.signOut}>
              <Button type='submit' color='secondary' variant='bordered'>
                Sign Out
              </Button>
            </form>
          </div>
        </PopoverContent>
      </Popover>
      // <div className='flex items-center gap-2'>
      //   <span className='text-sm'>
      //     {session.user.name || session.user.email}
      //   </span>
      // </div>
    );
  } else {
    // authContent = <div className='text-sm text-gray-600'>Signed Out</div>;
    authContent = (
      <>
        <NavbarItem>
          <form action={actions.signIn}>
            <Button type='submit' color='secondary' variant='bordered'>
              Sign In
            </Button>
          </form>
        </NavbarItem>
        <NavbarItem>
          <form action={actions.signIn}>
            <Button type='submit' color='primary' variant='flat'>Sign Up</Button>
          </form>
        </NavbarItem>
      </>
    );
  }

  return (
    <Navbar className='shadow-mb-6'>
      <NavbarBrand>
        <Link href='/' className='font-bold'>
          <p>Discuss</p>
        </Link>
      </NavbarBrand>
      <NavbarContent justify='center'>
        <NavbarItem>
          <Input placeholder='Search topics...' />
        </NavbarItem>
      </NavbarContent>
      <NavbarContent justify='end'>
        {/* <NavbarItem> */}
          {authContent}
          {/* {session?.user ? (
            <div className='flex items-center gap-2'>
              <span className='text-sm'>
                {session.user.name || session.user.email}
              </span>
            </div>
          ) : (
            <div className='text-sm text-gray-1000'>Signed Out</div>
          )} */}
          {/* {session?.user ? (
            <div className="flex items-center gap-2">
              {session.user.image && (
                <Avatar 
                  src={session.user.image} 
                  alt={session.user.name || 'User'} 
                  size="sm"
                />
              )}
              <span className="text-sm">
                {session.user.name || session.user.email}
              </span>
            </div>
          ) : (
            <div className="text-sm text-gray-600">
              Signed Out
            </div>
          )} */}
        {/* </NavbarItem> */}
      </NavbarContent>
    </Navbar>
  );
}
