'use client';

import { Button } from '@nextui-org/react';
import * as actions from '@/actions';
import { useFormStatus } from 'react-dom';


interface FormButtonProps {
  
  children: React.ReactNode;
}

export default function FormButton({ children }: FormButtonProps) {
  const { pending } = useFormStatus();
  return (
    <Button color='primary' type='submit' isLoading={pending}>
      {children}
    </Button>
  );
}
