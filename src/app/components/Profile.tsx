'use client';

import { useSession } from "next-auth/react";
import { But<PERSON> } from "@nextui-org/react";

export default function Profile() {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return <p>Loading...</p>;
  }

  if (status === "unauthenticated") {
    return <p>You are not signed in.</p>;
  }

  return (
    <div> 
      <h1 className='text-2xl font-bold'>Profile</h1>
      <p className='text-gray-600'>Name: {session?.user?.name}</p>
      <p className='text-gray-600'>Email: {session?.user?.email}</p>
      <p className='text-gray-600'>Image: <img src={session?.user?.image} alt="Profile" /></p>
    </div>
  );
}
