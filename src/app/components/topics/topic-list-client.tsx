'use client';

import Link from "next/link";
import { Chip } from "@nextui-org/react";
import paths from "@/paths";
import type { Topic } from "@prisma/client";

interface TopicListClientProps {
  topics: Topic[];
}

export default function TopicListClient({ topics }: TopicListClientProps) {
  const renderedTopics = topics.map((topic) => {
    return (
      <div key={topic.id}>
        <Link href={paths.topicShow(topic.slug)}>
          <Chip color="warning" variant="shadow">
            {topic.slug}
          </Chip>
        </Link>
      </div>
    );
  });
  
  return <div className="flex flex-row gap-2 flex-wrap">{renderedTopics}</div>;
}
