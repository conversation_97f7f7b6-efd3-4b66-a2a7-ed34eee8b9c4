

interface TopicShowPageProps {
  params: {
    slug: string;
  };
}

export default async function TopicShowPage({ params }: TopicShowPageProps) {
  const { slug } = (await props) params;

  return (
    <div className="grid grid-cols-4 gap-4 p-4">
      <div className="col-span-3">
        <h1 className="text-xl m-2">Topic: {slug}</h1>
      </div>
      <div className="border shadow py-3 px-2">
        <h3 className="text-lg">Posts</h3>
      </div>
    </div>
  )
}
