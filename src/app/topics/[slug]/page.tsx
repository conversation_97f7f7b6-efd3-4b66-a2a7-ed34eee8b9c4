import PostCreateForm from '@/app/components/posts/post-create-form';
import PostList from '@/app/components/posts/post-list';
import { fetchPostsByTopicSlug } from '@/db/queries/posts';

interface TopicShowPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export default async function TopicShowPage(props: TopicShowPageProps) {
  const { slug } = await props.params;

  return (
    <div className='grid grid-cols-4 gap-4 p-4'>
      <div className='col-span-3'>
        <h1 className='text-xl m-2'>Topic: {slug}</h1>
        <PostList fetchData={() => fetchPostsByTopicSlug(slug)} />
      </div>
      <div className='border shadow py-3 px-2'>
        <PostCreateForm slug={slug} />
      </div>
    </div>
  );
}
