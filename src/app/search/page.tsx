import { redirect } from 'next/navigation';
import PostList from '@/app/components/posts/post-list';
import { searchPosts } from '@/db/queries/posts';

interface SearchPageProps {
  searchParams: Promise<{ term?: string }>;
}

export default async function SearchPage({ searchParams }: SearchPageProps) {
  const { term } = await searchParams;

  console.log('SearchPage - term:', term);

  if (!term) {
    redirect('/');
  }

  return (
    <div className='grid grid-cols-4 gap-4 p-4'>
      <div className='col-span-3'>
        <h1 className='text-xl m-2'>Search Results for "{term}"</h1>
        <PostList fetchData={() => searchPosts(term)} />
      </div>
      <div className='col-span-1'>
        <div className='border shadow py-3 px-2'>
          <h3 className='text-lg'>Search Tips</h3>
          <p className='text-sm text-gray-600 mt-2'>
            Search for posts by title, content, author name, or topic.
          </p>
        </div>
      </div>
    </div>
  );
}
