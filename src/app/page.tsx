

import * as actions from '@/actions';
import { auth } from '@/auth';
import { But<PERSON> } from "@nextui-org/react";

export default async function Home() {
  const session = await auth();

  return (
    <div>
      <form action={actions.signIn}>
        <Button color='primary' type='submit'>
          Sign In
        </Button>
      </form>

      <form action={actions.signOut}>
        <Button color='primary' type='submit'>
          Sign Out
        </Button>
      </form>

      {session?.user? (
        <div>
          <h1>Welcome, {session.user.name}</h1>
        </div>
      )
      : (
        <div>
          <h1>Please sign in</h1>
        </div>
      )}

    </div>
  );
}
