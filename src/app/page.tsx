import { auth } from '@/auth';
import AuthButtons from '@/app/components/AuthButtons';
import Profile from '@/app/components/Profile';

export default async function Home() {
  const session = await auth();

  return (
    <div className='p-8'>
      <AuthButtons session={session} />

      {session?.user ? (
        <div className='mt-4'>
          <h1 className='text-2xl font-bold'>Welcome, {session.user.name}!</h1>
          <p className='text-gray-600'>You are signed in with GitHub.</p>
        </div>
      ) : (
        <div className='mt-4'>
          <h1 className='text-2xl font-bold'>Please sign in</h1>
          <p className='text-gray-600'>Sign in with GitHub to continue.</p>
        </div>
      )}

      {/* <Profile /> */}

    </div>
  );
}
