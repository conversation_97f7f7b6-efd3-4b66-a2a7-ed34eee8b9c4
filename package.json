{"name": "discussnew", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@nextui-org/react": "^2.6.11", "@prisma/client": "^6.11.0", "framer-motion": "^12.22.0", "next": "15.3.4", "next-auth": "^5.0.0-beta.29", "postcss": "^8.5.6", "prisma": "^6.11.0", "react": "^19.0.0", "react-dom": "^19.0.0", "zod": "^3.25.71"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}